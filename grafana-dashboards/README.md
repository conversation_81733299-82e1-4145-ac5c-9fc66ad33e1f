# OpenWrt Router Monitoring Dashboard

This Grafana dashboard provides comprehensive monitoring for your OpenWrt router with per-device traffic statistics, system metrics, and AdGuard Home monitoring.

## 📊 Dashboard Panels

### **1. Device Traffic - Bytes**
- **Purpose**: Shows real-time bandwidth usage for all connected devices
- **Metrics**: TX/RX bytes per device over time
- **Data Source**: `device_traffic` measurement from iptables accounting

### **2. Connected Devices Count**
- **Purpose**: Displays total number of devices with active DHCP leases
- **Metrics**: Count of unique devices from DHCP lease table
- **Data Source**: `dhcp_devices` measurement

### **3. CPU Usage**
- **Purpose**: Router CPU utilization percentage
- **Metrics**: CPU usage per core and total
- **Data Source**: `cpu` measurement (100% - idle%)

### **4. Memory Usage**
- **Purpose**: RAM utilization on the router
- **Metrics**: Memory used percentage
- **Data Source**: `mem` measurement

### **5. Interface Traffic Rate**
- **Purpose**: Network interface throughput rates
- **Metrics**: RX/TX bytes per second for br-lan, eth0, eth1, wlan0, wlan1
- **Data Source**: `interface_stats` measurement with derivative calculation

### **6. Connected Devices Table**
- **Purpose**: Detailed table of all connected devices
- **Columns**: 
  - Hostname (device name)
  - IP Address
  - MAC Address
  - Status (online/offline with color coding)
  - TX MB (data transmitted)
  - RX MB (data received)
- **Data Source**: `device_traffic` measurement with latest values

### **7. System Load Average**
- **Purpose**: System load metrics (1, 5, 15 minute averages)
- **Metrics**: Load1, Load5, Load15
- **Data Source**: `system` measurement

### **8. AdGuard Home Status**
- **Purpose**: Shows if AdGuard Home process is running
- **Metrics**: Process running status (1=Running, 0=Down)
- **Data Source**: `procstat` measurement for AdGuardHome pattern

### **9. Network Connectivity**
- **Purpose**: Internet connectivity and latency monitoring
- **Metrics**: Ping response times to *******, *******, and InfluxDB server
- **Data Source**: `ping` measurement

## 🚀 Installation Instructions

### **1. Import Dashboard**
1. Open Grafana web interface
2. Go to **Dashboards** → **Import**
3. Upload the `openwrt-monitoring.json` file
4. Configure data source (InfluxDB)

### **2. Configure Data Source**
1. Go to **Configuration** → **Data Sources**
2. Add **InfluxDB** data source
3. Configure connection:
   - **URL**: `http://192.168.8.101:8086`
   - **Database**: `default`
   - **Organization**: `home`
   - **Token**: Your InfluxDB token

### **3. Set Variables**
- The dashboard uses `${DS_INFLUXDB}` variable for data source
- Ensure your InfluxDB data source UID matches

## 📈 Key Features

### **Real-Time Device Monitoring**
- **Per-device bandwidth**: Actual traffic usage for each connected device
- **Device discovery**: Automatic detection via DHCP leases
- **Status tracking**: Online/offline status with visual indicators

### **System Health Monitoring**
- **Resource usage**: CPU, memory, and system load
- **Network performance**: Interface throughput and connectivity
- **Service monitoring**: AdGuard Home process status

### **Advanced Analytics**
- **Traffic patterns**: Historical bandwidth usage trends
- **Device behavior**: Connection patterns and usage statistics
- **Network topology**: Visual representation of connected devices

## 🔧 Customization

### **Time Ranges**
- Default: Last 1 hour with 30-second refresh
- Adjustable via Grafana time picker

### **Filtering**
- All queries filter by `host == "GL-MT6000"`
- Modify hostname in queries if your router has a different name

### **Thresholds**
- CPU/Memory: 80% warning threshold
- Connectivity: Response time thresholds
- Device status: Color-coded online/offline states

## 📋 Prerequisites

1. **OpenWrt router** with Telegraf configured
2. **InfluxDB v2** running and accessible
3. **Grafana** with InfluxDB data source
4. **Device traffic monitoring** enabled (iptables accounting)

## 🎯 Use Cases

- **Bandwidth monitoring**: Track which devices use most data
- **Network troubleshooting**: Identify connectivity issues
- **Capacity planning**: Monitor router resource usage
- **Security monitoring**: Detect unauthorized devices
- **Performance optimization**: Identify network bottlenecks

## 🔍 Troubleshooting

### **No Data Showing**
1. Verify InfluxDB connection
2. Check Telegraf is running on router
3. Confirm bucket name is "default"
4. Verify hostname matches "GL-MT6000"

### **Missing Device Data**
1. Check iptables rules are created
2. Verify DHCP leases exist
3. Ensure device traffic script is executable

### **AdGuard Home Not Showing**
1. Verify AdGuard Home is running
2. Check procstat pattern matches "AdGuardHome"
3. Confirm process monitoring is enabled

This dashboard provides enterprise-grade monitoring for your home OpenWrt router! 🎯
