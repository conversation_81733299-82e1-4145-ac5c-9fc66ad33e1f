---
- name: Install Python on OpenWrt router
  hosts: openwrt
  gather_facts: no
  tasks:
    - name: Update package lists
      ansible.builtin.raw: "opkg update"
      changed_when: false

    - name: Install Python3
      ansible.builtin.raw: "opkg install python3"
      register: python_install
      changed_when: "'Installing' in python_install.stdout"

    - name: Show Python installation result
      ansible.builtin.debug:
        var: python_install.stdout_lines

    - name: Verify Python3 installation
      ansible.builtin.raw: "which python3 && python3 --version"
      register: python_verify
      changed_when: false

    - name: Show Python verification
      ansible.builtin.debug:
        var: python_verify.stdout_lines

    - name: Install additional useful packages for monitoring
      ansible.builtin.raw: "opkg install {{ item }}"
      register: package_install
      changed_when: "'Installing' in package_install.stdout"
      failed_when: false
      loop:
        - curl
        - wget
        - htop
        - nano

    - name: Test Python functionality
      ansible.builtin.raw: 'python3 -c "import sys; print(\"Python\", sys.version, \"is working!\")"'
      register: python_test
      changed_when: false
      failed_when: false

    - name: Show Python test result
      ansible.builtin.debug:
        var: python_test.stdout_lines
      when: python_test.rc == 0
