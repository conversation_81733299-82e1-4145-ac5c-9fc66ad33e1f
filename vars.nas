{"ansible_facts": {"all_ipv4_addresses": ["**********", "**********", "*************", "**********", "**********", "***********", "***************", "**********"], "all_ipv6_addresses": ["fe80::42:17ff:fead:2c8", "fe80::5cdc:2fff:feaa:9402", "fe80::a48c:2eff:fe9e:83f2", "fe80::b848:57ff:febe:5c34", "fe80::48a2:87ff:fe14:bfc2", "fe80::fc7e:abff:fe07:3dc8", "fe80::8464:fff:fe77:6f1b", "fe80::4490:a4ff:fec3:6890", "fe80::f8fd:b7ff:fe02:d5d7", "fe80::a848:92ff:feee:6d76", "fe80::9cc3:5fff:fe96:ed1c", "fe80::42:caff:fee7:d94d", "fe80::50f5:fcff:fe3e:7435", "3ffe:501:ffff:100:211:32ff:fedb:5179", "fe80::211:32ff:fedb:5179", "fe80::42:c9ff:fe11:620e", "fe80::5443:87ff:fe38:c5ce"], "ansible_local": {}, "apparmor": {"status": "enabled"}, "architecture": "x86_64", "bios_date": "06/04/2021", "bios_vendor": "INSYDE Corp.", "bios_version": "M.119.00", "board_asset_tag": "Type2 - Board Asset Tag", "board_name": "DS720+", "board_serial": "123456789", "board_vendor": "Synology", "board_version": "1.0", "chassis_asset_tag": "<PERSON><PERSON><PERSON>", "chassis_serial": "123456789", "chassis_vendor": "Synology", "chassis_version": "1.0", "cmdline": {"HddEnableDynamicPower": "1", "SpectreAll_on": true, "intel_iommu": "igfx_off", "macs": "001132db5179,001132db517a", "netif_num": "2", "root": "/dev/md0", "sn": "2080QWR4PNB3Q", "syno_hw_version": "DS720+", "syno_ttyS0": "pciserial,0x0:0x18.0x2,115200", "syno_ttyS1": "pciserial,0x0:0x18.0x0,115200", "synoboot2": true, "vender_format_version": "2"}, "date_time": {"date": "2023-08-14", "day": "14", "epoch": "1692080648", "epoch_int": "1692080648", "hour": "23", "iso8601": "2023-08-15T06:24:08Z", "iso8601_basic": "20230814T232408966202", "iso8601_basic_short": "20230814T232408", "iso8601_micro": "2023-08-15T06:24:08.966202Z", "minute": "24", "month": "08", "second": "08", "time": "23:24:08", "tz": "PDT", "tz_dst": "PDT", "tz_offset": "-0700", "weekday": "Monday", "weekday_number": "1", "weeknumber": "33", "year": "2023"}, "default_ipv4": {"address": "***********", "alias": "eth0", "broadcast": "*************", "gateway": "***********", "interface": "eth0", "macaddress": "00:11:32:db:51:79", "mtu": 1500, "netmask": "*************", "network": "***********", "prefix": "24", "type": "ether"}, "default_ipv6": {}, "device_links": {"ids": {}, "labels": {}, "masters": {"dm-1": ["dm-2"], "md2": ["dm-0", "dm-1"], "sata1p1": ["md0"], "sata1p2": ["md1"], "sata1p3": ["md2"], "sata2p1": ["md0"], "sata2p2": ["md1"], "sata2p3": ["md2"]}, "uuids": {}}, "devices": {"dm-0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "24576", "sectorsize": "512", "size": "12.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "dm-1": {"holders": ["cachedev_0"], "host": "", "links": {"ids": [], "labels": [], "masters": ["dm-2"], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "7804362752", "sectorsize": "512", "size": "3.63 TB", "support_discard": "0", "vendor": null, "virtual": 1}, "dm-2": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "7804362752", "sectorsize": "512", "size": "3.63 TB", "support_discard": "0", "vendor": null, "virtual": 1}, "loop0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop1": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop2": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop3": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop4": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop5": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop6": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop7": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "md0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "4980352", "sectorsize": "512", "size": "2.37 GB", "support_discard": "0", "vendor": null, "virtual": 1}, "md1": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "4194176", "sectorsize": "512", "size": "2.00 GB", "support_discard": "0", "vendor": null, "virtual": 1}, "md2": {"holders": ["vg1-syno_vg_reserved_area", "vg1-volume_1"], "host": "", "links": {"ids": [], "labels": [], "masters": ["dm-0", "dm-1"], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "7804393088", "sectorsize": "512", "size": "3.63 TB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram1": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram10": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram11": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram12": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram13": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram14": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram15": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram2": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram3": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram4": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram5": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram6": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram7": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram8": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "ram9": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1310720", "sectorsize": "512", "size": "640.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "sata1": {"holders": [], "host": "Class 0106: Device 8086:31e3 (rev 06)", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": "ST4000VN008-2DR166", "partitions": {"sata1p1": {"holders": [], "links": {"ids": [], "labels": [], "masters": ["md0"], "uuids": []}, "sectors": "4980480", "sectorsize": 512, "size": "2.37 GB", "start": "2048", "uuid": null}, "sata1p2": {"holders": [], "links": {"ids": [], "labels": [], "masters": ["md1"], "uuids": []}, "sectors": "4194304", "sectorsize": 512, "size": "2.00 GB", "start": "4982528", "uuid": null}, "sata1p3": {"holders": [], "links": {"ids": [], "labels": [], "masters": ["md2"], "uuids": []}, "sectors": "7804395168", "sectorsize": 512, "size": "3.63 TB", "start": "9437184", "uuid": null}}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "cfq", "sectors": "7814037168", "sectorsize": "512", "size": "3.64 TB", "support_discard": "0", "vendor": "ATA", "virtual": 1}, "sata2": {"holders": [], "host": "Class 0106: Device 8086:31e3 (rev 06)", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": "ST4000VN008-2DR166", "partitions": {"sata2p1": {"holders": [], "links": {"ids": [], "labels": [], "masters": ["md0"], "uuids": []}, "sectors": "4980480", "sectorsize": 512, "size": "2.37 GB", "start": "2048", "uuid": null}, "sata2p2": {"holders": [], "links": {"ids": [], "labels": [], "masters": ["md1"], "uuids": []}, "sectors": "4194304", "sectorsize": 512, "size": "2.00 GB", "start": "4982528", "uuid": null}, "sata2p3": {"holders": [], "links": {"ids": [], "labels": [], "masters": ["md2"], "uuids": []}, "sectors": "7804395168", "sectorsize": 512, "size": "3.63 TB", "start": "9437184", "uuid": null}}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "cfq", "sectors": "7814037168", "sectorsize": "512", "size": "3.64 TB", "support_discard": "0", "vendor": "ATA", "virtual": 1}, "synoboot": {"holders": [], "host": "Class 0c03: Device 8086:31a8 (rev 06)", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": "DiskStation", "partitions": {"synoboot1": {"holders": [], "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "sectors": "65536", "sectorsize": 512, "size": "32.00 MB", "start": "2048", "uuid": null}, "synoboot2": {"holders": [], "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "sectors": "172032", "sectorsize": 512, "size": "84.00 MB", "start": "67584", "uuid": null}}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "cfq", "sectors": "245760", "sectorsize": "512", "size": "120.00 MB", "support_discard": "0", "vendor": "Synology", "virtual": 1}, "usb1": {"holders": [], "host": "Class 0c03: Device 8086:31a8 (rev 06)", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": "Wireless", "partitions": {"usb1p1": {"holders": [], "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "sectors": "1953519809", "sectorsize": 512, "size": "931.51 GB", "start": "256", "uuid": null}}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "cfq", "sectors": "1953525167", "sectorsize": "512", "size": "931.51 GB", "support_discard": "0", "vendor": "Seagate", "virtual": 1}, "zram0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1775616", "sectorsize": "4096", "size": "867.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "zram1": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1775616", "sectorsize": "4096", "size": "867.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "zram2": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1775616", "sectorsize": "4096", "size": "867.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}, "zram3": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "1775616", "sectorsize": "4096", "size": "867.00 MB", "support_discard": "4096", "vendor": null, "virtual": 1}}, "discovered_interpreter_python": "/usr/bin/python3.8", "distribution": "ClearLinux", "distribution_file_parsed": true, "distribution_file_path": "/usr/lib/os-release", "distribution_file_variety": "ClearLinux", "distribution_major_version": "NA", "distribution_release": "NA", "distribution_version": "NA", "dns": {"domain": "lan", "nameservers": ["*************", "*******"]}, "docker0": {"active": true, "device": "docker0", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [requested on]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [requested on]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "id": "8000.0242cae7d94d", "interfaces": ["dockerf159095", "docker7652518", "docker73d9b0a", "docker3d1590a", "dockera9e55b3", "dockerb843a9f", "dockere0336f2"], "ipv4": {"address": "**********", "broadcast": "**************", "netmask": "***********", "network": "**********", "prefix": "16"}, "ipv6": [{"address": "fe80::42:caff:fee7:d94d", "prefix": "64", "scope": "link"}], "macaddress": "02:42:ca:e7:d9:4d", "mtu": 1500, "promisc": false, "stp": false, "timestamping": ["rx_software", "software"], "type": "bridge"}, "docker2330efb": {"active": true, "device": "docker2330efb", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::48a2:87ff:fe14:bfc2", "prefix": "64", "scope": "link"}], "macaddress": "4a:a2:87:14:bf:c2", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "docker3d1590a": {"active": true, "device": "docker3d1590a", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::f8fd:b7ff:fe02:d5d7", "prefix": "64", "scope": "link"}], "macaddress": "fa:fd:b7:02:d5:d7", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "docker73d9b0a": {"active": true, "device": "docker73d9b0a", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::a848:92ff:feee:6d76", "prefix": "64", "scope": "link"}], "macaddress": "aa:48:92:ee:6d:76", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "docker7652518": {"active": true, "device": "docker7652518", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::4490:a4ff:fec3:6890", "prefix": "64", "scope": "link"}], "macaddress": "46:90:a4:c3:68:90", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "docker8d4dd7b": {"active": true, "device": "docker8d4dd7b", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::5443:87ff:fe38:c5ce", "prefix": "64", "scope": "link"}], "macaddress": "56:43:87:38:c5:ce", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "docker_084744f9": {"active": false, "device": "docker-084744f9", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "on", "tx_gre_segmentation": "on", "tx_gso_robust": "on", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "id": "8000.0242585b9fbe", "interfaces": [], "ipv4": {"address": "**********", "broadcast": "**************", "netmask": "***********", "network": "**********", "prefix": "16"}, "macaddress": "02:42:58:5b:9f:be", "mtu": 1500, "promisc": false, "stp": false, "timestamping": ["rx_software", "software"], "type": "bridge"}, "docker_5f288e80": {"active": false, "device": "docker-5f288e80", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "on", "tx_gre_segmentation": "on", "tx_gso_robust": "on", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "id": "8000.0242b6be7057", "interfaces": [], "ipv4": {"address": "**********", "broadcast": "**************", "netmask": "***********", "network": "**********", "prefix": "16"}, "macaddress": "02:42:b6:be:70:57", "mtu": 1500, "promisc": false, "stp": false, "timestamping": ["rx_software", "software"], "type": "bridge"}, "docker_cae3e578": {"active": true, "device": "docker-cae3e578", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [requested on]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [requested on]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "id": "8000.0242c911620e", "interfaces": ["dockerf510932", "docker2330efb"], "ipv4": {"address": "**********", "broadcast": "**************", "netmask": "***********", "network": "**********", "prefix": "16"}, "ipv6": [{"address": "fe80::42:c9ff:fe11:620e", "prefix": "64", "scope": "link"}], "macaddress": "02:42:c9:11:62:0e", "mtu": 1500, "promisc": false, "stp": false, "timestamping": ["rx_software", "software"], "type": "bridge"}, "docker_d107ca66": {"active": true, "device": "docker-d107ca66", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [requested on]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [requested on]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "id": "8000.024217ad02c8", "interfaces": ["docker8d4dd7b", "dockerb453a97"], "ipv4": {"address": "**********", "broadcast": "**************", "netmask": "***********", "network": "**********", "prefix": "16"}, "ipv6": [{"address": "fe80::42:17ff:fead:2c8", "prefix": "64", "scope": "link"}], "macaddress": "02:42:17:ad:02:c8", "mtu": 1500, "promisc": false, "stp": false, "timestamping": ["rx_software", "software"], "type": "bridge"}, "dockera9e55b3": {"active": true, "device": "dockera9e55b3", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::fc7e:abff:fe07:3dc8", "prefix": "64", "scope": "link"}], "macaddress": "fe:7e:ab:07:3d:c8", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "dockerb453a97": {"active": true, "device": "dockerb453a97", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::b848:57ff:febe:5c34", "prefix": "64", "scope": "link"}], "macaddress": "ba:48:57:be:5c:34", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "dockerb843a9f": {"active": true, "device": "dockerb843a9f", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::a48c:2eff:fe9e:83f2", "prefix": "64", "scope": "link"}], "macaddress": "a6:8c:2e:9e:83:f2", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "dockere0336f2": {"active": true, "device": "dockere0336f2", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::5cdc:2fff:feaa:9402", "prefix": "64", "scope": "link"}], "macaddress": "5e:dc:2f:aa:94:02", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "dockerf159095": {"active": true, "device": "dockerf159095", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::9cc3:5fff:fe96:ed1c", "prefix": "64", "scope": "link"}], "macaddress": "9e:c3:5f:96:ed:1c", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "dockerf510932": {"active": true, "device": "dockerf510932", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "on", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv6": [{"address": "fe80::50f5:fcff:fe3e:7435", "prefix": "64", "scope": "link"}], "macaddress": "52:f5:fc:3e:74:35", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": ["rx_software", "software"], "type": "ether"}, "domain": "", "effective_group_id": 0, "effective_user_id": 0, "env": {"HOME": "/root", "LOGNAME": "root", "MAIL": "/var/mail/root", "PATH": "/usr/bin:/bin:/usr/sbin:/sbin", "PWD": "/volume1/homes/manojm321", "SHELL": "/bin/ash", "SHLVL": "1", "SUDO_COMMAND": "/bin/sh -c echo BECOME-SUCCESS-rucyrrorvhfwldzyouwzepvrsulbkamw ; /usr/bin/python3.8 /var/services/homes/manojm321/.ansible/tmp/ansible-tmp-1692080646.281486-1467-81583677343130/AnsiballZ_setup.py", "SUDO_GID": "100", "SUDO_UID": "1026", "SUDO_USER": "manojm321", "TERM": "dumb", "USER": "root", "_": "/usr/bin/python3.8"}, "eth0": {"active": true, "device": "eth0", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off", "rx_checksumming": "on", "rx_fcs": "off", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "off [fixed]", "tx_checksum_ipv4": "on", "tx_checksum_ipv6": "on", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "off [fixed]", "tx_lockless": "off [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "off [fixed]", "tx_sit_segmentation": "off [fixed]", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "off [fixed]", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "off [fixed]", "udp_fragmentation_offload": "off [fixed]", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "***********", "broadcast": "*************", "netmask": "*************", "network": "***********", "prefix": "24"}, "ipv6": [{"address": "3ffe:501:ffff:100:211:32ff:fedb:5179", "prefix": "64", "scope": "global"}, {"address": "fe80::211:32ff:fedb:5179", "prefix": "64", "scope": "link"}], "macaddress": "00:11:32:db:51:79", "module": "r8168", "mtu": 1500, "pciid": "0000:04:00.0", "promisc": false, "speed": 1000, "timestamping": ["tx_software", "rx_software", "software"], "type": "ether"}, "eth1": {"active": false, "device": "eth1", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off", "rx_checksumming": "on", "rx_fcs": "off", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "off [fixed]", "tx_checksum_ipv4": "on", "tx_checksum_ipv6": "on", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "off [fixed]", "tx_lockless": "off [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "off [fixed]", "tx_sit_segmentation": "off [fixed]", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "off [fixed]", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "off [fixed]", "udp_fragmentation_offload": "off [fixed]", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "***************", "broadcast": "***************", "netmask": "***********", "network": "***********", "prefix": "16"}, "macaddress": "00:11:32:db:51:7a", "module": "r8168", "mtu": 1500, "pciid": "0000:03:00.0", "promisc": true, "speed": 65535, "timestamping": ["tx_software", "rx_software", "software"], "type": "ether"}, "fibre_channel_wwn": [], "fips": false, "form_factor": "Notebook", "fqdn": "localhost", "gather_subset": ["all"], "hostname": "mm_cloud", "hostnqn": "", "interfaces": ["dockerf159095", "dockerb843a9f", "dockera9e55b3", "docker8d4dd7b", "dockerf510932", "docker2330efb", "docker-5f288e80", "docker3d1590a", "eth0", "macvlan0", "docker-d107ca66", "docker73d9b0a", "eth1", "dockerb453a97", "docker7652518", "docker0", "dockere0336f2", "docker-084744f9", "docker-cae3e578", "sit0", "lo"], "is_chroot": false, "iscsi_iqn": "", "kernel": "4.4.180+", "kernel_version": "#42962 SMP Sat Apr 8 00:14:24 CST 2023", "lo": {"active": true, "device": "lo", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "on [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on [fixed]", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "on [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "off [fixed]", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off [fixed]", "tx_scatter_gather": "on [fixed]", "tx_scatter_gather_fraglist": "on [fixed]", "tx_sit_segmentation": "off [fixed]", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "off [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "udp_fragmentation_offload": "on", "vlan_challenged": "on [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "127.0.0.1", "broadcast": "", "netmask": "*********", "network": "*********", "prefix": "8"}, "ipv6": [{"address": "::1", "prefix": "128", "scope": "host"}], "mtu": 65536, "promisc": false, "timestamping": ["rx_software", "software"], "type": "loopback"}, "loadavg": {"15m": 0.22, "1m": 0.55, "5m": 0.3}, "lsb": {}, "lvm": {"lvs": {"syno_vg_reserved_area": {"size_g": "0.01", "vg": "vg1"}, "volume_1": {"size_g": "3721.41", "vg": "vg1"}}, "pvs": {"/dev/md2": {"free_g": "0", "size_g": "3721.42", "vg": "vg1"}}, "vgs": {"vg1": {"free_g": "0", "num_lvs": "2", "num_pvs": "1", "size_g": "3721.42"}}}, "machine": "x86_64", "machine_id": "307dfb624c2b4b5fbe0e1316bf75bd69", "macvlan0": {"active": true, "device": "macvlan0", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off", "loopback": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on [fixed]", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_robust": "on [fixed]", "tx_ipip_segmentation": "off [fixed]", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on [fixed]", "tx_scatter_gather_fraglist": "off [fixed]", "tx_sit_segmentation": "off [fixed]", "tx_tcp6_segmentation": "on [fixed]", "tx_tcp_ecn_segmentation": "on [fixed]", "tx_tcp_segmentation": "on [fixed]", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "on [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "udp_fragmentation_offload": "on [fixed]", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "*************", "broadcast": "", "netmask": "***************", "network": "*************", "prefix": "27"}, "ipv6": [{"address": "fe80::8464:fff:fe77:6f1b", "prefix": "64", "scope": "link"}], "macaddress": "86:64:0f:77:6f:1b", "mtu": 1500, "promisc": false, "speed": 65535, "timestamping": ["rx_software", "software"], "type": "ether"}, "memfree_mb": 135, "memory_mb": {"nocache": {"free": 4425, "used": 1351}, "real": {"free": 135, "total": 5776, "used": 5641}, "swap": {"cached": 38, "free": 5031, "total": 5515, "used": 484}}, "memtotal_mb": 5776, "module_setup": true, "mounts": [{"block_available": 67833, "block_size": 4096, "block_total": 596382, "block_used": 528549, "device": "/dev/md0", "fstype": "ext4", "inode_available": 113166, "inode_total": 155648, "inode_used": 42482, "mount": "/", "options": "rw,noatime,data=ordered", "size_available": 277843968, "size_total": 2442780672, "uuid": "N/A"}, {"block_available": 716780836, "block_size": 4096, "block_total": 936523531, "block_used": 219742695, "device": "/dev/mapper/cachedev_0", "fstype": "btrfs", "inode_available": 0, "inode_total": 0, "inode_used": 0, "mount": "/volume1", "options": "rw,nodev,relatime,ssd,synoacl,space_cache=v2,auto_reclaim_space,metadata_ratio=50,syno_allocator,subvolid=257,subvol=/@syno", "size_available": 2935934304256, "size_total": 3836000382976, "uuid": "N/A"}, {"block_available": 172606788, "block_size": 4096, "block_total": 240094748, "block_used": 67487960, "device": "/dev/usb1p1", "fstype": "ext4", "inode_available": 61024059, "inode_total": 61054976, "inode_used": 30917, "mount": "/volumeUSB1/usbshare", "options": "rw,relatime,nodelalloc,synoacl,data=ordered", "size_available": ************, "size_total": ************, "uuid": "N/A"}, {"block_available": 716780836, "block_size": 4096, "block_total": 936523531, "block_used": 219742695, "device": "/dev/mapper/cachedev_0", "fstype": "btrfs", "inode_available": 0, "inode_total": 0, "inode_used": 0, "mount": "/volume1/@docker/btrfs", "options": "rw,nodev,relatime,ssd,synoacl,space_cache=v2,auto_reclaim_space,metadata_ratio=50,syno_allocator,subvolid=257,subvol=/@syno/@docker/btrfs", "size_available": 2935934304256, "size_total": 3836000382976, "uuid": "N/A"}], "nodename": "mm_cloud", "os_family": "ClearLinux", "pkg_mgr": "unknown", "proc_cmdline": {"HddEnableDynamicPower": "1", "SpectreAll_on": true, "intel_iommu": "igfx_off", "macs": "001132db5179,001132db517a", "netif_num": "2", "root": "/dev/md0", "sn": "2080QWR4PNB3Q", "syno_hw_version": "DS720+", "syno_ttyS0": "pciserial,0x0:0x18.0x2,115200", "syno_ttyS1": "pciserial,0x0:0x18.0x0,115200", "synoboot2": true, "vender_format_version": "2"}, "processor": ["0", "GenuineIntel", "Intel(R) Celeron(R) J4125 CPU @ 2.00GHz", "1", "GenuineIntel", "Intel(R) Celeron(R) J4125 CPU @ 2.00GHz", "2", "GenuineIntel", "Intel(R) Celeron(R) J4125 CPU @ 2.00GHz", "3", "GenuineIntel", "Intel(R) Celeron(R) J4125 CPU @ 2.00GHz"], "processor_cores": 4, "processor_count": 1, "processor_nproc": 4, "processor_threads_per_core": 1, "processor_vcpus": 4, "product_name": "DS720+", "product_serial": "123456789", "product_uuid": "12345678-1234-5678-90AB-CDDEEFAABBCC", "product_version": "1.0", "python": {"executable": "/usr/bin/python3.8", "has_sslcontext": true, "type": "cpython", "version": {"major": 3, "micro": 12, "minor": 8, "releaselevel": "final", "serial": 0}, "version_info": [3, 8, 12, "final", 0]}, "python_version": "3.8.12", "real_group_id": 0, "real_user_id": 0, "selinux": {"status": "Missing selinux Python library"}, "selinux_python_present": false, "service_mgr": "systemd", "sit0": {"active": false, "device": "sit0", "features": {"busy_poll": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipip_segmentation": "off [fixed]", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sit_segmentation": "off [fixed]", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_segmentation": "on", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "off [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "udp_fragmentation_offload": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "macaddress": "00:00:00:00", "mtu": 1480, "promisc": false, "timestamping": ["rx_software", "software"], "type": "unknown"}, "ssh_host_key_dsa_public": "AAAAB3NzaC1kc3MAAACBAI15nYj+eXuIJ78Bj4kQ43xwirCzLQavDIHX6V8zMUHkdRKPdcOStgG2Qs4bSMvfN7NRuQRjAIsXg574dTXRkcXcgxcgPBGL/MWhpwFKF9yp5foIC/PNjhhjXXDuuuomhpeu17XHAjZYonfAI7KS7WQZ+QYSIwt4H/ERhoD5cM9PAAAAFQC9RkI3KXtEDAXPmZx1MqD6c91QQQAAAIAi6KiqOLFfJcGv/d38KnlNrRxoP7cNnlSfIlI8lYyrP+TGWZ8exBRF3CC3acAEY/1NVXspEIs9hevPRrt/D3KJY5DP/znX6KAqigqnigSDHYyUiVBYDpbzlye3tFDFLj0qibe6mQy6Nkks8sv6rnBEny3HoHhJZsPLivON5FG2SQAAAIBKXzuRKRK+c1R5lpUIu27+tpQsVmbAOG0jYwCU5LovMcGhfS6H41wbWVod4oS3/P47ZSjIIhzVXRzfKyxJR46FFFtZwtsl1MtGaaS9uEXwjpriYNIAnnnM7jsUzQMXxMYrSrMaMJmxQ7DBEUiLY3aVHjWktWW94Ld70YridZNF5w==", "ssh_host_key_dsa_public_keytype": "ssh-dss", "ssh_host_key_ecdsa_public": "AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBPkV43na+VzPMWFhjtX4XdhaXKAgr+chbUBwGfbLDlfHDmVpKwW0uIMHIVf5lHHfgJE92b1D/wmR2eeqqtc/fx0=", "ssh_host_key_ecdsa_public_keytype": "ecdsa-sha2-nistp256", "ssh_host_key_ed25519_public": "AAAAC3NzaC1lZDI1NTE5AAAAILVMdMApx4QWeCVPMRpPfBrPxxsi7Ui90muTMvwkS4wH", "ssh_host_key_ed25519_public_keytype": "ssh-ed25519", "ssh_host_key_rsa_public": "AAAAB3NzaC1yc2EAAAADAQABAAABAQC3LcmnpWURR+94IZDtbHB86ncVXjcHneHMHXuB74JMnqTRmHzKZbKxk4ezJK471c8Ejh+mBhb7vl7BTobHojBJNSsUs8wYo/9aek2nJBTRdjeN+dYrm1zBlgVE89kaoq+VsNnh1fwz2rRqVDVPnrdRHwhEuun3sJAhcGhUEbNLM8WDCAqyhNHB+z636Q5Imo8sCBTRbPvyRZt7PHLceHgg+T4R2/ppIRQmCxRJBevaBWDY4IlLMIDu2iV+DcKI/jL5N1eFx03HCJkGhytZc3OLhu/b3Qoa1LF0kDPJpcz/TU7RckrI/cNZnYxtcR8lWqX78S7LM2irtBAJR0ay4vQz", "ssh_host_key_rsa_public_keytype": "ssh-rsa", "swapfree_mb": 5031, "swaptotal_mb": 5515, "system": "Linux", "system_capabilities": ["cap_chown", "cap_dac_override", "cap_dac_read_search", "cap_fowner", "cap_fsetid", "cap_kill", "cap_setgid", "cap_setuid", "cap_setpcap", "cap_linux_immutable", "cap_net_bind_service", "cap_net_broadcast", "cap_net_admin", "cap_net_raw", "cap_ipc_lock", "cap_ipc_owner", "cap_sys_module", "cap_sys_rawio", "cap_sys_chroot", "cap_sys_ptrace", "cap_sys_pacct", "cap_sys_admin", "cap_sys_boot", "cap_sys_nice", "cap_sys_resource", "cap_sys_time", "cap_sys_tty_config", "cap_mknod", "cap_lease", "cap_audit_write", "cap_audit_control", "cap_setfcap", "cap_mac_override", "cap_mac_admin", "cap_syslog", "35", "36", "37+ep"], "system_capabilities_enforced": "True", "system_vendor": "Synology", "uptime_seconds": 244154, "user_dir": "/root", "user_gecos": "", "user_gid": 0, "user_id": "root", "user_shell": "/bin/ash", "user_uid": 0, "userspace_architecture": "x86_64", "userspace_bits": "64", "virtualization_role": "NA", "virtualization_tech_guest": [], "virtualization_tech_host": [], "virtualization_type": "NA"}}