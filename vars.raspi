{"ansible_facts": {"all_ipv4_addresses": ["***********4", "***************", "**********"], "all_ipv6_addresses": ["3ffe:501:ffff:100:a331:d3a9:bf07:d2e5", "fe80::c0d7:ee12:d9bb:5cd7", "fe80::9715:105c:aa30:2650", "fe80::42:9dff:fe1c:8368"], "ansible_local": {}, "apparmor": {"status": "disabled"}, "architecture": "armv7l", "bios_date": "", "bios_vendor": "", "bios_version": "", "board_asset_tag": "", "board_name": "", "board_serial": "", "board_vendor": "", "board_version": "", "chassis_asset_tag": "", "chassis_serial": "", "chassis_vendor": "", "chassis_version": "", "cmdline": {"8250.nr_uarts": "0", "coherent_pool": "1M", "console": "tty1", "fsck.repair": "yes", "plymouth.ignore-serial-consoles": true, "quiet": true, "root": "PARTUUID=c222de3d-02", "rootfstype": "ext4", "rootwait": true, "snd_bcm2835.enable_compat_alsa": "0", "snd_bcm2835.enable_hdmi": "1", "splash": true, "vc_mem.mem_base": "0x1ec00000", "vc_mem.mem_size": "0x20000000", "video": "Composite-1:720x480@60i"}, "date_time": {"date": "2023-08-15", "day": "15", "epoch": "1692080544", "epoch_int": "1692080544", "hour": "11", "iso8601": "2023-08-15T06:22:24Z", "iso8601_basic": "20230815T115224306515", "iso8601_basic_short": "20230815T115224", "iso8601_micro": "2023-08-15T06:22:24.306515Z", "minute": "52", "month": "08", "second": "24", "time": "11:52:24", "tz": "IST", "tz_dst": "IST", "tz_offset": "+0530", "weekday": "Tuesday", "weekday_number": "2", "weeknumber": "33", "year": "2023"}, "default_ipv4": {"address": "***********4", "alias": "eth0", "broadcast": "***********27", "gateway": "***********", "interface": "eth0", "macaddress": "8c:ae:4c:f5:4c:de", "mtu": 1500, "netmask": "***************", "network": "***********", "prefix": "25", "type": "ether"}, "default_ipv6": {}, "device_links": {"ids": {"mmcblk0": ["mmc-SS16G_0x0cc40875"], "mmcblk0p1": ["mmc-SS16G_0x0cc40875-part1"], "mmcblk0p2": ["mmc-SS16G_0x0cc40875-part2"]}, "labels": {"mmcblk0p1": ["boot"], "mmcblk0p2": ["rootfs"]}, "masters": {}, "uuids": {"mmcblk0p1": ["D386-9DE9"], "mmcblk0p2": ["996c1b5f-170b-4f38-a5e0-85eef5acf12c"]}}, "devices": {"loop0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop1": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop2": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop3": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop4": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop5": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop6": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "loop7": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "1", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "none", "sectors": "0", "sectorsize": "512", "size": "0.00 Bytes", "support_discard": "0", "vendor": null, "virtual": 1}, "mmcblk0": {"holders": [], "host": "", "links": {"ids": ["mmc-SS16G_0x0cc40875"], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {"mmcblk0p1": {"holders": [], "links": {"ids": ["mmc-SS16G_0x0cc40875-part1"], "labels": ["boot"], "masters": [], "uuids": ["D386-9DE9"]}, "sectors": "524288", "sectorsize": 512, "size": "256.00 MB", "start": "8192", "uuid": "D386-9DE9"}, "mmcblk0p2": {"holders": [], "links": {"ids": ["mmc-SS16G_0x0cc40875-part2"], "labels": ["rootfs"], "masters": [], "uuids": ["996c1b5f-170b-4f38-a5e0-85eef5acf12c"]}, "sectors": "30583808", "sectorsize": 512, "size": "14.58 GB", "start": "532480", "uuid": "996c1b5f-170b-4f38-a5e0-85eef5acf12c"}}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "mq-deadline", "sectors": "31116288", "sectorsize": "512", "serial": "0x0cc40875", "size": "14.84 GB", "support_discard": "4194304", "vendor": null, "virtual": 1}, "ram0": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram1": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram10": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram11": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram12": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram13": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram14": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram15": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram2": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram3": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram4": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram5": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram6": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram7": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram8": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}, "ram9": {"holders": [], "host": "", "links": {"ids": [], "labels": [], "masters": [], "uuids": []}, "model": null, "partitions": {}, "removable": "0", "rotational": "0", "sas_address": null, "sas_device_handle": null, "scheduler_mode": "", "sectors": "8192", "sectorsize": "512", "size": "4.00 MB", "support_discard": "0", "vendor": null, "virtual": 1}}, "discovered_interpreter_python": "/usr/bin/python3", "distribution": "Debian", "distribution_file_parsed": true, "distribution_file_path": "/etc/os-release", "distribution_file_variety": "Debian", "distribution_major_version": "11", "distribution_minor_version": "5", "distribution_release": "bullseye", "distribution_version": "11", "dns": {"nameservers": ["***********", "::"]}, "docker0": {"active": true, "device": "docker0", "features": {"esp_hw_offload": "off [fixed]", "esp_tx_csum_hw_offload": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on", "hsr_dup_offload": "off [fixed]", "hsr_fwd_offload": "off [fixed]", "hsr_tag_ins_offload": "off [fixed]", "hsr_tag_rm_offload": "off [fixed]", "hw_tc_offload": "off [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "macsec_hw_offload": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_gro_hw": "off [fixed]", "rx_gro_list": "off", "rx_udp_gro_forwarding": "off", "rx_udp_tunnel_port_offload": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tls_hw_record": "off [fixed]", "tls_hw_rx_offload": "off [fixed]", "tls_hw_tx_offload": "off [fixed]", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "on", "tx_esp_segmentation": "on", "tx_fcoe_segmentation": "off [requested on]", "tx_gre_csum_segmentation": "on", "tx_gre_segmentation": "on", "tx_gso_list": "on", "tx_gso_partial": "on", "tx_gso_robust": "off [requested on]", "tx_ipxip4_segmentation": "on", "tx_ipxip6_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sctp_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_mangleid_segmentation": "on", "tx_tcp_segmentation": "on", "tx_tunnel_remcsum_segmentation": "on", "tx_udp_segmentation": "on", "tx_udp_tnl_csum_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "id": "8000.02429d1c8368", "interfaces": ["vethe54263a"], "ipv4": {"address": "**********", "broadcast": "**************", "netmask": "***********", "network": "**********", "prefix": "16"}, "ipv6": [{"address": "fe80::42:9dff:fe1c:8368", "prefix": "64", "scope": "link"}], "macaddress": "02:42:9d:1c:83:68", "mtu": 1500, "promisc": false, "speed": 10000, "stp": false, "timestamping": [], "type": "bridge"}, "domain": "", "effective_group_id": 0, "effective_user_id": 0, "env": {"HOME": "/root", "LANG": "en_GB.UTF-8", "LOGNAME": "root", "MAIL": "/var/mail/root", "PATH": "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "PWD": "/home/<USER>", "SHELL": "/bin/bash", "SUDO_COMMAND": "/bin/sh -c echo BECOME-SUCCESS-chllpfimwpezcdatjwnjfyaihytdsjzs ; /usr/bin/python3 /home/<USER>/.ansible/tmp/ansible-tmp-1692080541.50789-1441-156228801349666/AnsiballZ_setup.py", "SUDO_GID": "1000", "SUDO_UID": "1000", "SUDO_USER": "pi", "TERM": "dumb", "USER": "root"}, "eth0": {"active": true, "device": "eth0", "features": {"esp_hw_offload": "off [fixed]", "esp_tx_csum_hw_offload": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "off [requested on]", "highdma": "off [fixed]", "hsr_dup_offload": "off [fixed]", "hsr_fwd_offload": "off [fixed]", "hsr_tag_ins_offload": "off [fixed]", "hsr_tag_rm_offload": "off [fixed]", "hw_tc_offload": "off [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "macsec_hw_offload": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_gro_hw": "off [fixed]", "rx_gro_list": "off", "rx_udp_gro_forwarding": "off", "rx_udp_tunnel_port_offload": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "off", "tcp_segmentation_offload": "off", "tls_hw_record": "off [fixed]", "tls_hw_rx_offload": "off [fixed]", "tls_hw_tx_offload": "off [fixed]", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "off [fixed]", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "off", "tx_esp_segmentation": "off [fixed]", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_csum_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_list": "off [fixed]", "tx_gso_partial": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipxip4_segmentation": "off [fixed]", "tx_ipxip6_segmentation": "off [fixed]", "tx_lockless": "off [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "off [fixed]", "tx_scatter_gather_fraglist": "off [fixed]", "tx_sctp_segmentation": "off [fixed]", "tx_tcp6_segmentation": "off [fixed]", "tx_tcp_ecn_segmentation": "off [fixed]", "tx_tcp_mangleid_segmentation": "off [fixed]", "tx_tcp_segmentation": "off [fixed]", "tx_tunnel_remcsum_segmentation": "off [fixed]", "tx_udp_segmentation": "off [fixed]", "tx_udp_tnl_csum_segmentation": "off [fixed]", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "off [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "***********4", "broadcast": "***********27", "netmask": "***************", "network": "***********", "prefix": "25"}, "ipv6": [{"address": "3ffe:501:ffff:100:a331:d3a9:bf07:d2e5", "prefix": "64", "scope": "global"}, {"address": "fe80::c0d7:ee12:d9bb:5cd7", "prefix": "64", "scope": "link"}], "macaddress": "8c:ae:4c:f5:4c:de", "module": "asix", "mtu": 1500, "pciid": "1-1:1.0", "promisc": false, "speed": 100, "timestamping": [], "type": "ether"}, "fibre_channel_wwn": [], "fips": false, "form_factor": "", "fqdn": "raspberry<PERSON>", "gather_subset": ["all"], "hostname": "raspberry<PERSON>", "hostnqn": "", "interfaces": ["docker0", "lo", "wlan0", "vethe54263a", "eth0"], "is_chroot": false, "iscsi_iqn": "", "kernel": "5.15.61-v7+", "kernel_version": "#1579 SMP Fri Aug 26 11:10:59 BST 2022", "lo": {"active": true, "device": "lo", "features": {"esp_hw_offload": "off [fixed]", "esp_tx_csum_hw_offload": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "on", "highdma": "on [fixed]", "hsr_dup_offload": "off [fixed]", "hsr_fwd_offload": "off [fixed]", "hsr_tag_ins_offload": "off [fixed]", "hsr_tag_rm_offload": "off [fixed]", "hw_tc_offload": "off [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "on [fixed]", "macsec_hw_offload": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on [fixed]", "rx_fcs": "off [fixed]", "rx_gro_hw": "off [fixed]", "rx_gro_list": "off", "rx_udp_gro_forwarding": "off", "rx_udp_tunnel_port_offload": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tls_hw_record": "off [fixed]", "tls_hw_rx_offload": "off [fixed]", "tls_hw_tx_offload": "off [fixed]", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on [fixed]", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "on [fixed]", "tx_checksumming": "on", "tx_esp_segmentation": "off [fixed]", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_csum_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_list": "on", "tx_gso_partial": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipxip4_segmentation": "off [fixed]", "tx_ipxip6_segmentation": "off [fixed]", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off [fixed]", "tx_scatter_gather": "on [fixed]", "tx_scatter_gather_fraglist": "on [fixed]", "tx_sctp_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_mangleid_segmentation": "on", "tx_tcp_segmentation": "on", "tx_tunnel_remcsum_segmentation": "off [fixed]", "tx_udp_segmentation": "on", "tx_udp_tnl_csum_segmentation": "off [fixed]", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "off [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "vlan_challenged": "on [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "127.0.0.1", "broadcast": "", "netmask": "*********", "network": "*********", "prefix": "8"}, "ipv6": [{"address": "::1", "prefix": "128", "scope": "host"}], "mtu": 65536, "promisc": false, "timestamping": [], "type": "loopback"}, "loadavg": {"15m": 0.05, "1m": 0.03, "5m": 0.07}, "lsb": {"codename": "bullseye", "description": "Raspbian GNU/Linux 11 (bullseye)", "id": "Raspbian", "major_release": "11", "release": "11"}, "lvm": "N/A", "machine": "armv7l", "machine_id": "5913752142cb4214a25dc48046cef34b", "memfree_mb": 108, "memory_mb": {"nocache": {"free": 279, "used": 147}, "real": {"free": 108, "total": 426, "used": 318}, "swap": {"cached": 3, "free": 1, "total": 99, "used": 98}}, "memtotal_mb": 426, "module_setup": true, "mounts": [{"block_available": 2196108, "block_size": 4096, "block_total": 3744532, "block_used": 1548424, "device": "/dev/root", "fstype": "ext4", "inode_available": 783261, "inode_total": 950976, "inode_used": 167715, "mount": "/", "options": "rw,noatime", "size_available": 8995258368, "size_total": 15337603072, "uuid": "N/A"}, {"block_available": 104435, "block_size": 2048, "block_total": 130554, "block_used": 26119, "device": "/dev/mmcblk0p1", "fstype": "vfat", "inode_available": 0, "inode_total": 0, "inode_used": 0, "mount": "/boot", "options": "rw,relatime,fmask=0022,dmask=0022,codepage=437,iocharset=ascii,shortname=mixed,errors=remount-ro", "size_available": 213882880, "size_total": 267374592, "uuid": "D386-9DE9"}], "nodename": "raspberry<PERSON>", "os_family": "Debian", "pkg_mgr": "apt", "proc_cmdline": {"8250.nr_uarts": "0", "coherent_pool": "1M", "console": ["ttyS0,115200", "tty1"], "fsck.repair": "yes", "plymouth.ignore-serial-consoles": true, "quiet": true, "root": "PARTUUID=c222de3d-02", "rootfstype": "ext4", "rootwait": true, "snd_bcm2835.enable_compat_alsa": "0", "snd_bcm2835.enable_hdmi": "1", "splash": true, "vc_mem.mem_base": "0x1ec00000", "vc_mem.mem_size": "0x20000000", "video": "Composite-1:720x480@60i"}, "processor": ["0", "ARMv7 Processor rev 4 (v7l)", "1", "ARMv7 Processor rev 4 (v7l)", "2", "ARMv7 Processor rev 4 (v7l)", "3", "ARMv7 Processor rev 4 (v7l)"], "processor_cores": 1, "processor_count": 4, "processor_nproc": 4, "processor_threads_per_core": 1, "processor_vcpus": 4, "product_name": "", "product_serial": "", "product_uuid": "", "product_version": "", "python": {"executable": "/usr/bin/python3", "has_sslcontext": true, "type": "cpython", "version": {"major": 3, "micro": 2, "minor": 9, "releaselevel": "final", "serial": 0}, "version_info": [3, 9, 2, "final", 0]}, "python_version": "3.9.2", "real_group_id": 0, "real_user_id": 0, "selinux": {"status": "disabled"}, "selinux_python_present": true, "service_mgr": "systemd", "ssh_host_key_dsa_public": "AAAAB3NzaC1kc3MAAACBANxTqVPEBZjz7rkE/UZ3cVcvTln/SCsUlqCs9QmHi8U1CVxCuRowHHqcsBJPiOyWH9NfMdozpSqHdCZxHoCSNsFZtLxJCOUHGUsA4nUJVWuOKmWOJfhAKs7ev29j/MMXHTEk0AQxD5Bu+hawrE0z/ESoM6MZ+6UT1H7LTtY8Ze51AAAAFQDE36EmhmRq8v2xo2EVddTeF48Q+QAAAIBQDwZiOt7NPcM+oecMkM9/Js4KYpGtI6e4cWwv4KetzIaoM8lgY2OWh0JkB6Q2h7dELzkwbIMOjA4jR2nG6uANJvqpXgaNmUwoXNOiaEQ5t1ZAtPsULjQb9gjCQmWIaK21m9lJQfq3Lat3xEeMTt+PjxEN7ZLyvp2mr0QeqxbXlwAAAIEA0cxvwR6V9TCBUJ+KPadPIA5huRzP1iKCStyQ4z+9eeAZvitxOBO3/vlgpkL3ur8bDZYMgff7YzIgpc4Ak5YpSvUqI4OOHj9eDHQdCk2plwvn25ykg+MizYjoSREON7PXPxEwY8W5H53Uw5r4fxDpuaEaHZ3vo6Lvci7wZ8SMHGc=", "ssh_host_key_dsa_public_keytype": "ssh-dss", "ssh_host_key_ecdsa_public": "AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBAEpYzFU0yyhTxJoiT5ge/dXGtNXcVbfQAvI5UQdi+7mHP5ryLQPtbou93JarLIVkzA09DSM+u18tM0V5npFDPw=", "ssh_host_key_ecdsa_public_keytype": "ecdsa-sha2-nistp256", "ssh_host_key_ed25519_public": "AAAAC3NzaC1lZDI1NTE5AAAAIG6q2ofOS1/qfu78oRGRPHg8SqjpNlebJbpbERyOaUbC", "ssh_host_key_ed25519_public_keytype": "ssh-ed25519", "ssh_host_key_rsa_public": "AAAAB3NzaC1yc2EAAAADAQABAAABgQDx9Kq45UWU6ujZlb3O0s+sl5hlhgnew7/jyldeDqQ/mnRkpbbYpBAc++CoVTrqWhD/343jMhvOKVBM2kZom9f3TnBlPdn5uVuMmmDQccLQKdQ+S9PVljMYYJBBdHJhz/5wOrrSP9ArPGpyS7NS9NC4t+rLRTiTtL/Zi+OzvOfSETDNsDG3BR/TQz9h2TvaJIzgcZF+AmNx8EkacmHvuPK/c+yCrwpfls1AEteWF+PiH6SCw0NXSRrsYaMp3oqM2AZmPQzk2cPLPhmSTIQWzDWRnI3PvMQU7vmVWHwf8ch2Q84ij0rWRr31eC2/ZTvBWHaKMu0Zkfna0o2RD5+xvBfKQ9x/VoRxU+8HOfYKAKQtZyOfO4MvZv27LFg2GmVazgsj4m+NQ6Ld9/bkJWKQ6GioP7tekS4yX4UhKaqIvovzgrPE5XgzzYGcYn7z7AxHROKCgpvSY43hRrTeuxUfD/8U/5KwAGGp1JGh/t5sgLXFRYXEp3Y4r1dq+3u17pvHFvc=", "ssh_host_key_rsa_public_keytype": "ssh-rsa", "swapfree_mb": 1, "swaptotal_mb": 99, "system": "Linux", "system_capabilities": [], "system_capabilities_enforced": "False", "system_vendor": "", "uptime_seconds": 154666, "user_dir": "/root", "user_gecos": "root", "user_gid": 0, "user_id": "root", "user_shell": "/bin/bash", "user_uid": 0, "userspace_bits": "32", "vethe54263a": {"active": true, "device": "vethe54263a", "features": {"esp_hw_offload": "off [fixed]", "esp_tx_csum_hw_offload": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "off", "generic_segmentation_offload": "on", "highdma": "on", "hsr_dup_offload": "off [fixed]", "hsr_fwd_offload": "off [fixed]", "hsr_tag_ins_offload": "off [fixed]", "hsr_tag_rm_offload": "off [fixed]", "hw_tc_offload": "off [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "macsec_hw_offload": "off [fixed]", "netns_local": "off [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "on", "rx_fcs": "off [fixed]", "rx_gro_hw": "off [fixed]", "rx_gro_list": "off", "rx_udp_gro_forwarding": "off", "rx_udp_tunnel_port_offload": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "on", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "on", "scatter_gather": "on", "tcp_segmentation_offload": "on", "tls_hw_record": "off [fixed]", "tls_hw_rx_offload": "off [fixed]", "tls_hw_tx_offload": "off [fixed]", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "on", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "on", "tx_checksumming": "on", "tx_esp_segmentation": "off [fixed]", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_csum_segmentation": "on", "tx_gre_segmentation": "on", "tx_gso_list": "on", "tx_gso_partial": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipxip4_segmentation": "on", "tx_ipxip6_segmentation": "on", "tx_lockless": "on [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "on", "tx_scatter_gather_fraglist": "on", "tx_sctp_segmentation": "on", "tx_tcp6_segmentation": "on", "tx_tcp_ecn_segmentation": "on", "tx_tcp_mangleid_segmentation": "on", "tx_tcp_segmentation": "on", "tx_tunnel_remcsum_segmentation": "off [fixed]", "tx_udp_segmentation": "on", "tx_udp_tnl_csum_segmentation": "on", "tx_udp_tnl_segmentation": "on", "tx_vlan_offload": "on", "tx_vlan_stag_hw_insert": "on", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "ipv4": {"address": "***************", "broadcast": "***************", "netmask": "***********", "network": "***********", "prefix": "16"}, "ipv6": [{"address": "fe80::9715:105c:aa30:2650", "prefix": "64", "scope": "link"}], "macaddress": "ca:86:bf:06:71:bc", "mtu": 1500, "promisc": true, "speed": 10000, "timestamping": [], "type": "ether"}, "virtualization_role": "NA", "virtualization_tech_guest": [], "virtualization_tech_host": [], "virtualization_type": "NA", "wlan0": {"active": false, "device": "wlan0", "features": {"esp_hw_offload": "off [fixed]", "esp_tx_csum_hw_offload": "off [fixed]", "fcoe_mtu": "off [fixed]", "generic_receive_offload": "on", "generic_segmentation_offload": "off [requested on]", "highdma": "off [fixed]", "hsr_dup_offload": "off [fixed]", "hsr_fwd_offload": "off [fixed]", "hsr_tag_ins_offload": "off [fixed]", "hsr_tag_rm_offload": "off [fixed]", "hw_tc_offload": "off [fixed]", "l2_fwd_offload": "off [fixed]", "large_receive_offload": "off [fixed]", "loopback": "off [fixed]", "macsec_hw_offload": "off [fixed]", "netns_local": "on [fixed]", "ntuple_filters": "off [fixed]", "receive_hashing": "off [fixed]", "rx_all": "off [fixed]", "rx_checksumming": "off [fixed]", "rx_fcs": "off [fixed]", "rx_gro_hw": "off [fixed]", "rx_gro_list": "off", "rx_udp_gro_forwarding": "off", "rx_udp_tunnel_port_offload": "off [fixed]", "rx_vlan_filter": "off [fixed]", "rx_vlan_offload": "off [fixed]", "rx_vlan_stag_filter": "off [fixed]", "rx_vlan_stag_hw_parse": "off [fixed]", "scatter_gather": "off", "tcp_segmentation_offload": "off", "tls_hw_record": "off [fixed]", "tls_hw_rx_offload": "off [fixed]", "tls_hw_tx_offload": "off [fixed]", "tx_checksum_fcoe_crc": "off [fixed]", "tx_checksum_ip_generic": "off [fixed]", "tx_checksum_ipv4": "off [fixed]", "tx_checksum_ipv6": "off [fixed]", "tx_checksum_sctp": "off [fixed]", "tx_checksumming": "off", "tx_esp_segmentation": "off [fixed]", "tx_fcoe_segmentation": "off [fixed]", "tx_gre_csum_segmentation": "off [fixed]", "tx_gre_segmentation": "off [fixed]", "tx_gso_list": "off [fixed]", "tx_gso_partial": "off [fixed]", "tx_gso_robust": "off [fixed]", "tx_ipxip4_segmentation": "off [fixed]", "tx_ipxip6_segmentation": "off [fixed]", "tx_lockless": "off [fixed]", "tx_nocache_copy": "off", "tx_scatter_gather": "off [fixed]", "tx_scatter_gather_fraglist": "off [fixed]", "tx_sctp_segmentation": "off [fixed]", "tx_tcp6_segmentation": "off [fixed]", "tx_tcp_ecn_segmentation": "off [fixed]", "tx_tcp_mangleid_segmentation": "off [fixed]", "tx_tcp_segmentation": "off [fixed]", "tx_tunnel_remcsum_segmentation": "off [fixed]", "tx_udp_segmentation": "off [fixed]", "tx_udp_tnl_csum_segmentation": "off [fixed]", "tx_udp_tnl_segmentation": "off [fixed]", "tx_vlan_offload": "off [fixed]", "tx_vlan_stag_hw_insert": "off [fixed]", "vlan_challenged": "off [fixed]"}, "hw_timestamp_filters": [], "macaddress": "b8:27:eb:24:89:ec", "module": "brcmfmac", "mtu": 1500, "pciid": "mmc1:0001:1", "promisc": false, "timestamping": [], "type": "ether"}}}