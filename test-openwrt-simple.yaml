---
- name: Test OpenWrt router connectivity (no facts)
  hosts: openwrt
  gather_facts: no
  tasks:
    - name: Test basic connection
      ansible.builtin.ping:

    - name: Check available Python interpreters
      ansible.builtin.raw: "which python3 || which python || echo 'No Python found'"
      register: python_check
      changed_when: false

    - name: Show Python interpreter locations
      ansible.builtin.debug:
        var: python_check.stdout_lines

    - name: Check OpenWrt version
      ansible.builtin.raw: "cat /etc/openwrt_release"
      register: openwrt_version
      changed_when: false

    - name: Show OpenWrt version
      ansible.builtin.debug:
        var: openwrt_version.stdout_lines

    - name: Check system info
      ansible.builtin.raw: "uname -a && uptime"
      register: system_info
      changed_when: false

    - name: Show system info
      ansible.builtin.debug:
        var: system_info.stdout_lines
