---
- name: Test Telegraf on OpenWrt
  hosts: openwrt
  gather_facts: no
  tasks:
    - name: Check Telegraf process
      ansible.builtin.raw: "ps | grep telegraf | grep -v grep"
      register: telegraf_process
      changed_when: false

    - name: Show Telegraf process
      ansible.builtin.debug:
        var: telegraf_process.stdout_lines

    - name: Check Telegraf configuration
      ansible.builtin.raw: "telegraf --test --config /etc/telegraf/telegraf.conf | head -20"
      register: telegraf_test
      changed_when: false
      failed_when: false

    - name: Show Telegraf test output
      ansible.builtin.debug:
        var: telegraf_test.stdout_lines

    - name: Check if AdGuard Home is accessible
      ansible.builtin.raw: "curl -s http://127.0.0.1:3000/control/stats | head -5"
      register: adguard_test
      changed_when: false
      failed_when: false

    - name: Show AdGuard accessibility
      ansible.builtin.debug:
        msg: "AdGuard Home API is {{ 'accessible' if adguard_test.rc == 0 else 'not accessible' }}"

    - name: Check Telegraf logs
      ansible.builtin.raw: "logread | grep telegraf | tail -10"
      register: telegraf_logs
      changed_when: false
      failed_when: false

    - name: Show recent Telegraf logs
      ansible.builtin.debug:
        var: telegraf_logs.stdout_lines
      when: telegraf_logs.stdout_lines | length > 0
