---
- name: Test OpenWrt router connectivity
  hosts: openwrt
  gather_facts: yes
  tasks:
    - name: Test basic connection
      ansible.builtin.ping:
      register: ping_result

    - name: Display connection info
      ansible.builtin.debug:
        msg: |
          Connected to: {{ inventory_hostname }}
          IP Address: {{ ansible_default_ipv4.address | default('N/A') }}
          OS: {{ ansible_distribution | default('Unknown') }} {{ ansible_distribution_version | default('') }}
          Architecture: {{ ansible_architecture | default('N/A') }}
          Hostname: {{ ansible_hostname | default('N/A') }}

    - name: Check OpenWrt version
      ansible.builtin.command: cat /etc/openwrt_release
      register: openwrt_version
      failed_when: false
      changed_when: false

    - name: Show OpenWrt version
      ansible.builtin.debug:
        var: openwrt_version.stdout_lines
      when: openwrt_version.rc == 0

    - name: Check system uptime
      ansible.builtin.debug:
        msg: "Uptime: {{ ansible_uptime_seconds | default(0) | int // 3600 }}h {{ (ansible_uptime_seconds | default(0) | int % 3600) // 60 }}m"

    - name: Check memory usage
      ansible.builtin.debug:
        msg: "Memory: {{ ansible_memtotal_mb | default(0) }}MB total, {{ ansible_memfree_mb | default(0) }}MB free"
      when: ansible_memtotal_mb is defined
