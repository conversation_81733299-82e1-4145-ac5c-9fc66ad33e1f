########
# docker socket is owned by root. Move it to a group so that other users can be
# added later. Ex: telegraf

- name: Users in docker group
  shell: cat /etc/group | grep docker | sed 's/.*://'
  register: docker

- name: Split list
  set_fact:
    docker_list: "{{ docker.stdout.split(',') }}"

- name: Create docker group
  when: docker.stdout.find("root") == -1
  command: /usr/syno/sbin/synogroup --add docker {{ ' '.join(docker_list) }} root

- name: Move socket to docker group
  file:
    path: /var/run/docker.sock
    group: docker

# Docker looks for /etc/os-release file
- name: Synlink /etc/VERSION
  ansible.builtin.file:
    src: /etc/VERSION
    dest: /usr/lib/os-release
    owner: root
    group: root
    state: link

- name: Install volume size metrics script
  ansible.builtin.copy:
    src: "docker-volume-size-metrics.sh"
    dest: "/usr/bin/docker-volume-size-metrics"
    mode: "755"

- name: Copy telegraf config file
  ansible.builtin.template:
    src: "telegraf-docker-volume-size-metrics.conf"
    dest: "/etc/telegraf/telegraf.d/telegraf-docker-volume-size-metrics.conf"
    mode: 0644
  notify:
    - reload telegraf
