[[inputs.exec]]
  ## Commands array
  commands = [
    "/usr/bin/docker-volume-size-metrics",
  ]
  timeout = "60s"
  data_format = "influx"

[[outputs.influxdb_v2]]
  ## The URLs of the InfluxDB cluster nodes.
  ##
  ## Multiple URLs can be specified for a single cluster, only ONE of the
  ## urls will be written to each interval.
  ## urls exp: http://127.0.0.1:8086
  urls = ["http://localhost:8086"]

  ## Token for authentication.
  token = "{{ telegraf_influxdb_token }}"

  ## Organization is the name of the organization you wish to write to; must exist.
  organization = "home"

  ## Destination bucket to write into.
  bucket = "default"
  namepass = [ "docker_volume_fs_usage_bytes" ]
