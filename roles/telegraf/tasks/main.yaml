---
- include: install.yaml
- include: setup-user.yaml

- name: Copy config file
  ansible.builtin.copy:
    src: "telegraf.conf"
    dest: "/etc/telegraf/telegraf.conf"
    mode: 0644
  notify:
    - reload telegraf

- name: Copy output plugin config file
  ansible.builtin.template:
    src: "influxdb-output.conf"
    dest: "/etc/telegraf/telegraf.d/influxdb-output.conf"
    mode: 0644
  notify:
    - systemd reload
    - reload telegraf

- name: Make sure a service unit is running
  ansible.builtin.systemd:
    state: started
    name: telegraf
