########
# create telegraf user so that we can add it to wheel/docker groups. wheel to
# allow password less sudo. docker - to access docker socket

# create user
- name: Read /etc/passwd file
  shell: cat /etc/passwd
  register: etc_passwd

- name: Create user telegraf
  when: etc_passwd.stdout.find("telegraf") == -1
  # NB: args are [username passwd "full name" expired{0|1} mail AppPrivilege]
  command: /usr/syno/sbin/synouser --add telegraf "" "" 0 "" 0
  args:
    creates: /volume1/homes/telegraf

# add to wheel
- name: Get list of wheel group users
  shell: cat /etc/group | grep wheel | sed 's/.*://'
  register: wheel

- name: Split list
  set_fact:
    wheel_list: "{{ wheel.stdout.split(',') }}"

- name: Add telegraf to wheel group
  when: wheel.stdout.find("telegraf") == -1
  command: /usr/syno/sbin/synogroup --member wheel {{ ' '.join(wheel_list) }} telegraf

# add to docker group
- name: Get list of members
  shell: cat /etc/group | grep docker | sed 's/.*://'
  register: docker

- name: Split list
  set_fact:
    docker_list: "{{ docker.stdout.split(',') }}"

- name: Add user to docker group
  when: docker.stdout.find("telegraf") == -1
  command: /usr/syno/sbin/synogroup --member docker {{ ' '.join(docker_list) }} telegraf
