[[outputs.influxdb_v2]]
  alias = "metrics"
  ## The URLs of the InfluxDB cluster nodes.
  ##
  ## Multiple URLs can be specified for a single cluster, only ONE of the
  ## urls will be written to each interval.
  ## urls exp: http://127.0.0.1:8086
  urls = ["http://localhost:8086"]

  ## Token for authentication.
  token = "{{ telegraf_influxdb_token }}"

  ## Organization is the name of the organization you wish to write to; must exist.
  organization = "home"

  ## Destination bucket to write into.
  bucket = "default"
  namepass = [ "cpu", "disk", "diskio", "mem", "net", "processes", "swap", "kernel", "system", "temp", "docker*", "internet_speed" ]

[[outputs.influxdb_v2]]
  alias = "logging"
  ## The URLs of the InfluxDB cluster nodes.
  ##
  ## Multiple URLs can be specified for a single cluster, only ONE of the
  ## urls will be written to each interval.
  ## urls exp: http://127.0.0.1:8086
  urls = ["http://localhost:8086"]

  ## Token for authentication.
  token = "{{ influxdb_log_bucket }}"

  ## Organization is the name of the organization you wish to write to; must exist.
  organization = "home"

  ## Destination bucket to write into.
  bucket = "log"
  namepass = [ "syslog", "varlog" ]