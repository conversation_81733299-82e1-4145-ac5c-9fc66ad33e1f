---
- name: Create a network
  community.docker.docker_network:
    name: monitoring

- name: Create a volume
  community.docker.docker_volume:
    name: influxdb2

- include_role:
    name: backup
  vars:
    volume_name: influxdb2

- name: Influxdb container
  community.docker.docker_container:
    name: influxdb
    image: "influxdb"
    restart_policy: unless-stopped
    container_default_behavior: no_defaults
    log_driver: "json-file"
    log_options: "max-size=10m"
    volumes:
      - "influxdb2:/var/lib/influxdb2"
    network_mode: default
    networks:
      - name: monitoring
      - name: hass-network
    ports:
      - "8086:8086/tcp"
    env:
      # 7 days
      INFLUXD_SESSION_LENGTH: "10080"
    healthcheck:
      test: ["CMD", "curl", "http://localhost:8086/ping"]
      interval: 10s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      docker-volume-backup.stop-during-backup: "influxdb2"
