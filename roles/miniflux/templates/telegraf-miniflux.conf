[[inputs.prometheus]]
  ## An array of urls to scrape metrics from.
  urls = ["http://localhost:8081/metrics"]

  tagexclude = [ "url" ]
  [inputs.prometheus.tags]
    app = "miniflux"

[[outputs.influxdb_v2]]
  ## The URLs of the InfluxDB cluster nodes.
  ##
  ## Multiple URLs can be specified for a single cluster, only ONE of the
  ## urls will be written to each interval.
  ## urls exp: http://127.0.0.1:8086
  urls = ["http://localhost:8086"]

  ## Token for authentication.
  token = "{{ telegraf_influxdb_token }}"

  ## Organization is the name of the organization you wish to write to; must exist.
  organization = "home"

  ## Destination bucket to write into.
  bucket = "default"
  tagpass = [ "miniflux*" ]
