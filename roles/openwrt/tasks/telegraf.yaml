---
# Telegraf installation and configuration for OpenWrt

- name: Check if Telegraf is available in OpenWrt packages
  ansible.builtin.raw: "opkg list | grep telegraf"
  register: telegraf_available
  changed_when: false
  failed_when: false
  tags: telegraf

- name: Install Telegraf from OpenWrt packages
  ansible.builtin.raw: "opkg install telegraf"
  register: telegraf_install
  changed_when: "'Installing' in telegraf_install.stdout"
  when: telegraf_available.rc == 0
  tags: telegraf

- name: Create Telegraf directories
  ansible.builtin.raw: "mkdir -p /etc/telegraf /var/log/telegraf"
  changed_when: false
  tags: telegraf

- name: Create Telegraf configuration
  ansible.builtin.template:
    src: telegraf.conf.j2
    dest: /etc/telegraf/telegraf.conf
    mode: '0644'
  notify: restart telegraf
  tags: telegraf

- name: Create device traffic monitoring script
  ansible.builtin.copy:
    dest: /usr/bin/device-traffic-stats.sh
    mode: '0755'
    content: |
      #!/bin/sh
      # Enhanced device traffic monitoring script for OpenWrt with per-device stats

      CHAIN_NAME="TELEGRAF_ACCOUNTING"

      # Function to setup iptables rules for device accounting
      setup_iptables_rules() {
          # Check if our custom chain exists, create if not
          if ! iptables -t filter -L "$CHAIN_NAME" >/dev/null 2>&1; then
              iptables -t filter -N "$CHAIN_NAME"
              # Insert rule to jump to our chain for LAN traffic
              iptables -t filter -I FORWARD -i br-lan -j "$CHAIN_NAME"
              iptables -t filter -I FORWARD -o br-lan -j "$CHAIN_NAME"
          fi

          # Get current DHCP leases and ensure rules exist for each device
          while read lease_time mac ip hostname rest; do
              if [ -n "$ip" ] && [ "$ip" != "0.0.0.0" ]; then
                  # Check if rules already exist for this IP
                  if ! iptables -t filter -C "$CHAIN_NAME" -s "$ip" >/dev/null 2>&1; then
                      iptables -t filter -A "$CHAIN_NAME" -s "$ip"
                  fi
                  if ! iptables -t filter -C "$CHAIN_NAME" -d "$ip" >/dev/null 2>&1; then
                      iptables -t filter -A "$CHAIN_NAME" -d "$ip"
                  fi
              fi
          done < /tmp/dhcp.leases
      }

      # Setup iptables rules first
      setup_iptables_rules

      # Get per-device traffic statistics from iptables
      while read lease_time mac ip hostname rest; do
          if [ -n "$ip" ] && [ "$ip" != "0.0.0.0" ]; then
              # Clean hostname for InfluxDB (replace spaces and special chars)
              clean_hostname=$(echo "$hostname" | sed 's/[^a-zA-Z0-9_-]/_/g')

              # Check if device is reachable
              if ping -c 1 -W 1 "$ip" >/dev/null 2>&1; then
                  status="online"
              else
                  status="offline"
              fi

              # Get traffic stats from iptables for this IP
              tx_stats=$(iptables -t filter -L "$CHAIN_NAME" -v -n | grep "^[[:space:]]*[0-9]" | grep "$ip[[:space:]]*0\.0\.0\.0/0" | head -1)
              rx_stats=$(iptables -t filter -L "$CHAIN_NAME" -v -n | grep "^[[:space:]]*[0-9]" | grep "0\.0\.0\.0/0[[:space:]]*$ip$" | head -1)

              # Function to convert iptables byte format to raw bytes
              convert_bytes() {
                  local value="$1"
                  case "$value" in
                      *K) echo $(($(echo "$value" | sed 's/K//') * 1024)) ;;
                      *M) echo $(($(echo "$value" | sed 's/M//') * 1024 * 1024)) ;;
                      *G) echo $(($(echo "$value" | sed 's/G//') * 1024 * 1024 * 1024)) ;;
                      *) echo "$value" ;;
                  esac
              }

              if [ -n "$tx_stats" ]; then
                  tx_packets_raw=$(echo "$tx_stats" | awk '{print $1}')
                  tx_packets=$(convert_bytes "$tx_packets_raw")
                  tx_bytes_raw=$(echo "$tx_stats" | awk '{print $2}')
                  tx_bytes=$(convert_bytes "$tx_bytes_raw")
              else
                  tx_packets=0
                  tx_bytes=0
              fi

              if [ -n "$rx_stats" ]; then
                  rx_packets_raw=$(echo "$rx_stats" | awk '{print $1}')
                  rx_packets=$(convert_bytes "$rx_packets_raw")
                  rx_bytes_raw=$(echo "$rx_stats" | awk '{print $2}')
                  rx_bytes=$(convert_bytes "$rx_bytes_raw")
              else
                  rx_packets=0
                  rx_bytes=0
              fi

              # Output device traffic stats
              echo "device_traffic,ip=$ip,mac=$mac,hostname=$clean_hostname tx_bytes=${tx_bytes}i,tx_packets=${tx_packets}i,rx_bytes=${rx_bytes}i,rx_packets=${rx_packets}i,status=\"$status\""
          fi
      done < /tmp/dhcp.leases

      # Get overall interface statistics
      while read line; do
          case "$line" in
              *br-lan:*|*eth*:*|*wlan*:*)
                  interface=$(echo "$line" | cut -d: -f1 | tr -d ' ')
                  rx_bytes=$(echo "$line" | awk '{print $2}')
                  rx_packets=$(echo "$line" | awk '{print $3}')
                  tx_bytes=$(echo "$line" | awk '{print $10}')
                  tx_packets=$(echo "$line" | awk '{print $11}')

                  echo "interface_stats,interface=$interface rx_bytes=${rx_bytes}i,rx_packets=${rx_packets}i,tx_bytes=${tx_bytes}i,tx_packets=${tx_packets}i"
                  ;;
          esac
      done < /proc/net/dev
  tags: telegraf

- name: Create iptables setup script for device accounting
  ansible.builtin.copy:
    dest: /usr/bin/setup-device-accounting.sh
    mode: '0755'
    content: |
      #!/bin/sh
      # Setup iptables rules for device traffic accounting

      CHAIN_NAME="TELEGRAF_ACCOUNTING"

      # Remove existing chain if it exists
      iptables -t filter -D FORWARD -i br-lan -j "$CHAIN_NAME" 2>/dev/null
      iptables -t filter -D FORWARD -o br-lan -j "$CHAIN_NAME" 2>/dev/null
      iptables -t filter -F "$CHAIN_NAME" 2>/dev/null
      iptables -t filter -X "$CHAIN_NAME" 2>/dev/null

      # Create new chain
      iptables -t filter -N "$CHAIN_NAME"

      # Insert rules to jump to our chain for LAN traffic
      iptables -t filter -I FORWARD -i br-lan -j "$CHAIN_NAME"
      iptables -t filter -I FORWARD -o br-lan -j "$CHAIN_NAME"

      echo "Device accounting iptables rules setup complete"
  tags: telegraf

- name: Run iptables setup script
  ansible.builtin.raw: "/usr/bin/setup-device-accounting.sh"
  changed_when: false
  tags: telegraf

- name: Enable and start Telegraf service
  ansible.builtin.service:
    name: telegraf
    enabled: yes
    state: started
  tags: telegraf
