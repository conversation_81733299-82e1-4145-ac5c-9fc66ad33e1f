# Telegraf Configuration for OpenWrt Router

# Global tags
[global_tags]
  role = "router"
  device_type = "openwrt"
  router_ip = "{{ inventory_hostname }}"

# Agent configuration
[agent]
  interval = "30s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  collection_jitter = "5s"
  flush_interval = "30s"
  flush_jitter = "5s"
  precision = ""
  hostname = ""
  omit_hostname = false

###############################################################################
#                            OUTPUT PLUGINS                                  #
###############################################################################

# InfluxDB v2 output
[[outputs.influxdb_v2]]
  urls = ["http://{{ influxdb_host }}:8086"]
  token = "{{ telegraf_influxdb_token }}"
  organization = "{{ influxdb_org | default('home') }}"
  bucket = "default"

###############################################################################
#                            INPUT PLUGINS                                   #
###############################################################################

# System metrics
[[inputs.cpu]]
  percpu = true
  totalcpu = true
  collect_cpu_time = false
  report_active = false

[[inputs.disk]]
  ignore_fs = ["tmpfs", "devtmpfs", "devfs", "overlay", "aufs", "squashfs"]

[[inputs.diskio]]

[[inputs.mem]]

[[inputs.net]]
  interfaces = ["br-lan", "eth0", "eth1", "wlan0", "wlan1"]

[[inputs.netstat]]

[[inputs.processes]]

[[inputs.system]]

[[inputs.kernel]]

# Wireless statistics
[[inputs.wireless]]

# Network connectivity tests
[[inputs.ping]]
  urls = ["*******", "*******", "{{ influxdb_host }}"]
  count = 3
  timeout = 1.0
  deadline = 10

# AdGuard Home process monitoring
[[inputs.procstat]]
  pattern = "AdGuardHome"
  pid_tag = true

# AdGuard Home port monitoring
[[inputs.net_response]]
  protocol = "tcp"
  address = "127.0.0.1:3000"
  timeout = "3s"
  send = ""
  expect = ""
  tags = { service = "adguard_web" }

[[inputs.net_response]]
  protocol = "tcp"
  address = "127.0.0.1:3053"
  timeout = "3s"
  send = ""
  expect = ""
  tags = { service = "adguard_dns" }

# DNS functionality test
[[inputs.dns_query]]
  servers = ["127.0.0.1"]
  port = 3053
  domains = ["google.com", "cloudflare.com"]
  record_type = "A"
  timeout = 3

# AdGuard Home HTTP endpoint monitoring
[[inputs.http_response]]
  urls = ["http://127.0.0.1:3000"]
  method = "GET"
  response_timeout = "3s"
  tags = { service = "adguard_http" }

# Monitor AdGuard Home database files
[[inputs.filestat]]
  files = ["/etc/AdGuardHome/data/stats.db", "/etc/AdGuardHome/data/sessions.db"]
  tags = { service = "adguard" }

# DHCP lease monitoring for device discovery
[[inputs.exec]]
  commands = ["awk '{print \"dhcp_lease,mac=\" $2 \",ip=\" $3 \",hostname=\" $4 \" lease_time=\" $1}' /tmp/dhcp.leases"]
  timeout = "5s"
  data_format = "influx"
  name_override = "dhcp_devices"

# ARP table monitoring for device presence
[[inputs.exec]]
  commands = ["awk '/^[0-9]/ {gsub(/[()]/,\"\"); print \"arp_entry,ip=\" $1 \",interface=\" $3 \",mac=\" $4 \" state=\\\"\" $6 \"\\\"\"}' /proc/net/arp"]
  timeout = "5s"
  data_format = "influx"
  name_override = "arp_table"

# Device traffic monitoring script
[[inputs.exec]]
  commands = ["/usr/bin/device-traffic-stats.sh"]
  timeout = "10s"
  data_format = "influx"
  name_override = "device_traffic"


