- name: Create a network
  community.docker.docker_network:
    name: gramps

- name: Create Gramps volumes
  community.docker.docker_volume:
    volume_name: "{{ item }}"
  loop:
    - gramps-users
    - gramps-index
    - gramps-thumb-cache
    - gramps-cache
    - gramps-secret
    - gramps-db
    - gramps-media
    - gramps-tmp

- name: Create Gramps Redis container
  community.docker.docker_container:
    name: gramps-redis
    image: redis
    restart_policy: unless-stopped
    container_default_behavior: no_defaults
    log_driver: "json-file"
    log_options: "max-size=10m"
    networks:
      - name: gramps

- name: Create Gramps container
  community.docker.docker_container: &gramps_container
    name: gramps
    image: ghcr.io/gramps-project/grampsweb:latest
    env:
      GRAMPSWEB_TREE: "Gramps"
      GRAMPSWEB_CELERY_CONFIG__broker_url: "redis://gramps-redis:6379/0"
      GRAMPSWEB_CELERY_CONFIG__result_backend: "redis://gramps-redis:6379/0"
      GRAMPSWEB_RATELIMIT_STORAGE_URI: redis://gramps-redis:6379/1
      GRAMPSWEB_REGISTRATION_DISABLED: "True"
      GUNICORN_NUM_WORKERS: "2"
    volumes:
      - gramps-users:/app/users
      - gramps-index:/app/indexdir
      - gramps-thumb-cache:/app/thumbnail_cache
      - gramps-cache:/app/cache
      - gramps-secret:/app/secret
      - gramps-db:/root/.gramps/grampsdb
      - gramps-media:/app/media
      - gramps-tmp:/tmp
    ports: "8088:5000"
    networks:
      - name: gramps

- name: Create Gramps Celery container
  community.docker.docker_container:
    <<: *gramps_container
    name: gramps-celery
    command: "celery -A gramps_webapi.celery worker --loglevel=INFO --concurrency=2"
    ports: []
