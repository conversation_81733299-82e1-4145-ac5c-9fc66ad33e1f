---
- name: Setup Synology NAS
  hosts: nas
  become: true
  roles:
    - {role: system, tags: "system"}
    - {role: python, tags: "python"}
    - {role: docker, tags: "docker"}
    - {role: telegraf, tags: "telegraf"}
    - {role: grafana, tags: "grafana"}
    - {role: influxdb, tags: "influxdb"}
    - {role: watchtower, tags: "watchtower"}
    - {role: fava, tags: "fava"}
    - {role: miniflux, tags: "miniflux"}
    - {role: paperless-ng, tags: "paperless-ng"}
    - {role: gramps, tags: "gramps"}
    - {role: homeassistant, tags: "hass"}

- name: Setup OpenWrt Router
  hosts: openwrt
  become: true
  roles:
    - {role: openwrt, tags: "openwrt"}

# Uncomment and configure as needed
# - name: Setup Raspberry Pi
#   hosts: pi
#   become: true
#   tasks:
#     - name: Print all available facts
#       ansible.builtin.debug:
#         var: ansible_facts
