# Kubernetes (k3s) Ansible Setup

This directory contains Ansible playbooks and roles for setting up k3s and deploying services on your homelab infrastructure.

## Directory Structure

```
k8s/
├── ansible.cfg          # Ansible configuration
├── hosts               # Inventory file
├── main.yaml           # Main playbook
├── group_vars/         # Group variables
│   └── all             # Variables for all hosts
└── roles/              # Ansible roles
    ├── k3s/            # k3s installation role
    ├── influxdb/       # InfluxDB k8s deployment role
    ├── system/         # System setup role (copied from parent)
    ├── python/         # Python setup role (copied from parent)
    └── openwrt/        # OpenWrt setup role (copied from parent)
```

## Usage

### Prerequisites

1. Ensure you have the Kubernetes collection installed:
   ```bash
   ansible-galaxy collection install kubernetes.core
   ```

2. Install kubectl on your local machine to interact with the cluster.

### Running the Playbook

1. Navigate to the k8s directory:
   ```bash
   cd k8s
   ```

2. Run the full playbook:
   ```bash
   ansible-playbook main.yaml --vault-password-file ~/pwd
   ```

3. Run specific roles with tags:
   ```bash
   # Install k3s only
   ansible-playbook main.yaml --vault-password-file ~/pwd --tags k3s
   
   # Deploy InfluxDB only
   ansible-playbook main.yaml --vault-password-file ~/pwd --tags influxdb
   ```

## k3s Configuration

- **Data Directory**: `/var/services/homes/manojm321/k3Volumes/k3s-data`
- **Volume Directory**: `/var/services/homes/manojm321/k3Volumes`
- **Kubeconfig**: `/home/<USER>/.kube/config`

## Services

### InfluxDB

- **Namespace**: default
- **Service Type**: NodePort
- **External Port**: 30086
- **Internal Port**: 8086
- **Storage**: 10Gi persistent volume at `/var/services/homes/manojm321/k3Volumes/influxdb-data`

Access InfluxDB at: `http://192.168.8.101:30086`

## Migration from Docker

This setup is designed to incrementally migrate services from Docker to k3s. The original Docker-based setup remains in the parent directory and can run in parallel during the migration process.

## Next Steps

1. Test the k3s installation and InfluxDB deployment
2. Migrate additional services (Grafana, Telegraf, etc.)
3. Update monitoring configurations to use the new k8s endpoints
4. Gradually phase out Docker-based services
