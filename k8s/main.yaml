---
- name: Setup k3s on Synology NAS
  hosts: nas
  roles:
    - {role: system, tags: "system"}
    - {role: k3s, tags: "k3s"}
    - {role: influxdb, tags: "influxdb"}

- name: Setup OpenWrt Router
  hosts: openwrt
  become: true
  roles:
    - {role: openwrt, tags: "openwrt"}

# Uncomment and configure as needed
# - name: Setup Raspberry Pi
#   hosts: pi
#   become: true
#   tasks:
#     - name: Print all available facts
#       ansible.builtin.debug:
#         var: ansible_facts
