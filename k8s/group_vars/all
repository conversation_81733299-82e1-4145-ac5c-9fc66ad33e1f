---
# ansible-vault encrypt_string --vault-password-file <pass-file> --name=<var-name> 'secret' | pbcopy
telegraf_version: "1.26.2"
telegraf_tar_checksum: "sha256:139b41ba79deadc26a4d73c9e77fcc3b84f847720423a14c1e7ac6cec2275375"
macvlan_ip: "*************"

# InfluxDB configuration
influxdb_org: "home"
influxdb_bucket: "default"
influxdb_host: "*************"

# OpenWrt configuration
openwrt_adguard_port: "3000"
openwrt_adguard_host: "127.0.0.1"
telegraf_influxdb_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          34376537366536613330663831383437633030356636303033383737373838306639633939336464
          6366633138316465633331313930626136356139363666620a613636666465373138623439393631
          65343239623034383037313830393638613564323262386330363939396564616637343762363566
          6238313634663333300a326334306532346136303934666130636333303533366636613432336637
          65303330393265386637343865623761613038353163623438376238333263396331643835663938
          33333732363033313731636137613762663161376562386466643133346633333739643039373236
          63623434326333303536613130373539316366646161383865393664323465633633386466333530
          36613033323031333331316439376237646438613936643366616466336463336361393066653330
          3637

miniflux_postgres_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          39393838616631636431613764666630343862643035323033373130373262376330666332376139
          3562656231633234353161666137613234616638333633340a306364643532393266663564333030
          34303437633433303962346235393839373636336662626636363630346539386433663764346661
          6632303363343861650a633333646330323563363266336535643561366162633637613733323039
          38326561613462396261313831383539613035346464393932356561386366363135
miniflux_admin_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          33343139343162653363653062643466656235643634613931343261313739633764383337366235
          3564623063393534663931616330306237383637363333370a636434646662646566316238376530
          66626565666333666137396531396165636364643031353463343039343833303135303963326462
          6230326162336461650a653234346361383837653837666530616563636339663832346230303461
          6462
paperless_ng_db_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35323437393739653134323132353338633163393636333638633730323034396437366639653630
          3866353037363763343162633630363237393834623664330a373533356562346466663630336561
          65623563386435306264616366623939356563303962633162656162363862623535366132643531
          3537316663643635320a383234356237303032653337613563653963663466393463343037613632
          66386331623661623365316237666430623562393935333930623537346639373739
paperless_ng_secret_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          30393632343565343166663238386663373833643634343236633238373337303462303137653234
          3136653230383330643861636561383936346334623539320a653833623361313064366262323735
          64373530323265356164303530613237646265353631366563656330393038336636346633373232
          3538633465633535320a613831366439393939393235376166343366633731313331613739643563
          31393332616263366132633064653731633138346562616164643631326463376632
paperless_ng_admin_name: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          61386530656435633033323164386563363363363332383535363063663833636361356661316364
          6138373132326432323965336261303364663061366136380a666134393137636165303230373163
          37316162356531346433383061376134346366316339643732393933356133353539383665393635
          3364326337393330300a666336366462633836306139323139613437396536323330366634346133
          6161
paperless_ng_admin_pass: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          37613836613463306363356137393432323039663164383237323562363761376634393262623665
          3364313734393764353635303731646339656631323266620a616162386337356630303239663439
          33363466336532366262663435613166323537356335323731393763306165336439333732393734
          3462636666323336390a306631623937383661663363396435346264636139303237616362313939
          30316536333331316362633866613431353564363862303030326461356665353939
influxdb_log_bucket: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35333034366461626631316433363066373333313961343538363730386633613435633031373864
          3039303966633431393632633732333533613163633763620a356638316464633737306238376231
          37313762386134396532663530346530623738353736383432663737343130353730306236653930
          3365616137653561370a643732366336633631613636326264653561393162626564313739376431
          31633539323566323230343534373734393734396339636434346662323937363637346238633564
          65373536653635643539613165643731356138366531326262646537656235336132373331306437
          31356533643137613137393039396435326237393231326331336464333464396266366166333037
          63333337393531336436663137643934383461643662306236393964396437663533646434616139
          6136
hass_influxdb_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          62303363393265393230623835303534313966316639316565346134643565366631323465316635
          3566663536343632396531653561646332303538303032320a313337656630653339646439326434
          66633734303535653237353339633066356166633965363161646332306230343937356631663437
          3832346139633739310a323939653636626661366238323964306436393837363938323938626561
          66343432343538356332353334663135656266383661353934343932633236666465656630396635
          30303932376665313037633332663339633331633232633834313537353865373739343736316632
          37393463313836343539613733613930653732626361633133326233613339613633646434373432
          66636335646338356561346262646362323431386165366537306232636564653032383339386330
          6334
