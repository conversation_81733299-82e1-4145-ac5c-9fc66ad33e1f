---
- name: Create InfluxDB data directory
  ansible.builtin.file:
    path: /var/services/homes/manojm321/k3Volumes/influxdb-data
    state: directory
    mode: '0755'
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"

- name: Create temporary directory for k8s manifests
  ansible.builtin.tempfile:
    state: directory
    suffix: influxdb-k8s
  register: temp_dir

- name: Copy InfluxDB PersistentVolume manifest
  ansible.builtin.template:
    src: influxdb-pv.yaml
    dest: "{{ temp_dir.path }}/influxdb-pv.yaml"
    mode: '0644'

- name: Copy InfluxDB PersistentVolumeClaim manifest
  ansible.builtin.template:
    src: influxdb-pvc.yaml
    dest: "{{ temp_dir.path }}/influxdb-pvc.yaml"
    mode: '0644'

- name: Copy InfluxDB Deployment manifest
  ansible.builtin.template:
    src: influxdb-deployment.yaml
    dest: "{{ temp_dir.path }}/influxdb-deployment.yaml"
    mode: '0644'

- name: Copy InfluxDB Service manifest
  ansible.builtin.template:
    src: influxdb-service.yaml
    dest: "{{ temp_dir.path }}/influxdb-service.yaml"
    mode: '0644'

- name: Apply InfluxDB PersistentVolume
  kubernetes.core.k8s:
    state: present
    src: "{{ temp_dir.path }}/influxdb-pv.yaml"
    kubeconfig: "/home/<USER>/.kube/config"

- name: Apply InfluxDB PersistentVolumeClaim
  kubernetes.core.k8s:
    state: present
    src: "{{ temp_dir.path }}/influxdb-pvc.yaml"
    kubeconfig: "/home/<USER>/.kube/config"

- name: Apply InfluxDB Deployment
  kubernetes.core.k8s:
    state: present
    src: "{{ temp_dir.path }}/influxdb-deployment.yaml"
    kubeconfig: "/home/<USER>/.kube/config"

- name: Apply InfluxDB Service
  kubernetes.core.k8s:
    state: present
    src: "{{ temp_dir.path }}/influxdb-service.yaml"
    kubeconfig: "/home/<USER>/.kube/config"

- name: Wait for InfluxDB deployment to be ready
  kubernetes.core.k8s_info:
    api_version: apps/v1
    kind: Deployment
    name: influxdb
    namespace: default
    kubeconfig: "/home/<USER>/.kube/config"
    wait: true
    wait_condition:
      type: Available
      status: "True"
    wait_timeout: 300

- name: Get InfluxDB pod status
  kubernetes.core.k8s_info:
    api_version: v1
    kind: Pod
    namespace: default
    label_selectors:
      - app=influxdb
    kubeconfig: "/home/<USER>/.kube/config"
  register: influxdb_pods

- name: Display InfluxDB pod status
  ansible.builtin.debug:
    msg: "InfluxDB pod status: {{ item.status.phase }}"
  loop: "{{ influxdb_pods.resources }}"
  when: influxdb_pods.resources | length > 0

- name: Clean up temporary directory
  ansible.builtin.file:
    path: "{{ temp_dir.path }}"
    state: absent
