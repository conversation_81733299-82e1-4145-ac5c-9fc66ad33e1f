---
- name: Apply InfluxDB all-in-one manifest
  kubernetes.core.k8s:
    state: present
    definition: "{{ lookup('template', 'influxdb-all.yaml') | from_yaml_all | list }}"
    context: "mm_cloud"

- name: Wait for InfluxDB deployment to be ready
  kubernetes.core.k8s_info:
    api_version: apps/v1
    kind: Deployment
    name: influxdb
    namespace: default
    context: "mm_cloud"
    wait: true
    wait_condition:
      type: Available
      status: "True"
    wait_timeout: 300

- name: Get InfluxDB pod status
  kubernetes.core.k8s_info:
    api_version: v1
    kind: Pod
    namespace: default
    label_selectors:
      - app=influxdb
    context: "mm_cloud"
  register: influxdb_pods

- name: Display InfluxDB pod status
  ansible.builtin.debug:
    msg: "InfluxDB pod status: {{ item.status.phase }}"
  loop: "{{ influxdb_pods.resources }}"
  when: influxdb_pods.resources | length > 0
