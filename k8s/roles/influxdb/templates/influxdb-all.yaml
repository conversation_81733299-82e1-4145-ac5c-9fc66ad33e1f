---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: influxdb-pv
  labels:
    app: influxdb
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: local-storage
  hostPath:
    path: /var/services/homes/manojm321/k3Volumes/influxdb-data
    type: DirectoryOrCreate
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: influxdb-pvc
  namespace: default
  labels:
    app: influxdb
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-storage
  selector:
    matchLabels:
      app: influxdb
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: influxdb
  namespace: default
  labels:
    app: influxdb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: influxdb
  template:
    metadata:
      labels:
        app: influxdb
    spec:
      containers:
      - name: influxdb
        image: influxdb:latest
        ports:
        - containerPort: 8086
          name: http
        env:
        - name: INFLUXD_SESSION_LENGTH
          value: "10080"
        volumeMounts:
        - name: influxdb-storage
          mountPath: /var/lib/influxdb2
        livenessProbe:
          httpGet:
            path: /ping
            port: 8086
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ping
            port: 8086
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: influxdb-storage
        persistentVolumeClaim:
          claimName: influxdb-pvc
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: influxdb
  namespace: default
  labels:
    app: influxdb
spec:
  type: NodePort
  ports:
  - port: 8086
    targetPort: 8086
    nodePort: 30086
    protocol: TCP
    name: http
  selector:
    app: influxdb
