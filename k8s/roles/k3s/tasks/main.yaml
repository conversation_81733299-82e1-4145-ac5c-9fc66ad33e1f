---
- name: Create k3s volumes directory
  ansible.builtin.file:
    path: /var/services/homes/manojm321/k3Volumes
    state: directory
    mode: '0755'
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"

- name: Check if k3s is already installed
  ansible.builtin.stat:
    path: /usr/local/bin/k3s
  register: k3s_binary

- name: Download k3s installation script
  ansible.builtin.get_url:
    url: https://get.k3s.io
    dest: /tmp/k3s-install.sh
    mode: '0755'
  when: not k3s_binary.stat.exists

- name: Install k3s
  ansible.builtin.shell: |
    INSTALL_K3S_EXEC="--data-dir=/var/services/homes/manojm321/k3Volumes/k3s-data" /tmp/k3s-install.sh
  environment:
    INSTALL_K3S_EXEC: "--data-dir=/var/services/homes/manojm321/k3Volumes/k3s-data"
  when: not k3s_binary.stat.exists
  register: k3s_install_result

- name: Wait for k3s to be ready
  ansible.builtin.wait_for:
    port: 6443
    host: localhost
    delay: 10
    timeout: 300
  when: k3s_install_result.changed

- name: Create kubeconfig directory
  ansible.builtin.file:
    path: "/home/<USER>/.kube"
    state: directory
    mode: '0755'
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"

- name: Copy k3s kubeconfig
  ansible.builtin.copy:
    src: /etc/rancher/k3s/k3s.yaml
    dest: "/home/<USER>/.kube/config"
    remote_src: true
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: '0600'

- name: Update kubeconfig server address
  ansible.builtin.replace:
    path: "/home/<USER>/.kube/config"
    regexp: 'https://127.0.0.1:6443'
    replace: 'https://{{ ansible_default_ipv4.address }}:6443'

- name: Ensure k3s service is started and enabled
  ansible.builtin.systemd:
    name: k3s
    state: started
    enabled: true

- name: Display k3s status
  ansible.builtin.command: systemctl status k3s --no-pager
  register: k3s_status
  changed_when: false

- name: Show k3s status
  ansible.builtin.debug:
    var: k3s_status.stdout_lines
