- name: Increase open file limit
  ansible.posix.sysctl:
    name: fs.file-max
    value: '500000'
    state: present

- name: Create a network with custom IPAM config
  community.docker.docker_network:
    name: macvlan
    driver: macvlan
    driver_options:
      parent: eth1
    ipam_config:
      - subnet: ***********/24
        gateway: ***********
        iprange: *************/27


- name: create macvlan
  shell: ip link add macvlan0 link eth1 type macvlan mode bridge
  when: "'macvlan0' not in ansible_interfaces"

- name: associate subnet
  shell: ip addr add *************/27 dev macvlan0
  when: "'macvlan0' not in ansible_interfaces"

- name: Script to create macvlan
  copy:
    mode: '+x'
    dest: /usr/local/bin/macvlan.sh
    content: |
      #!/usr/bin/env bash
      ip link add macvlan0 link eth1 type macvlan mode bridge
      ip route add *************/27 dev macvlan0
      ip link set macvlan0 up
      ip addr add *************/32 dev macvlan0
      ifconfig macvlan0

- name: create a systemd file
  copy:
    mode: '0644'
    dest: /usr/lib/systemd/system/macvlan.service
    content: |
      [Unit]
      Wants=network-online.target
      After=network-online.target

      [Service]
      Type=oneshot
      ExecStart=/usr/local/bin/macvlan.sh

      [Install]
      WantedBy=default.target

- name: Enable service
  ansible.builtin.systemd:
    name: macvlan
    enabled: true
    masked: no

